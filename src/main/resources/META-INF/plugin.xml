<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <id>com.github.iptton.kbuilder</id>
    <name>kbuilder</name>
    <vendor>iptton</vendor>

    <depends>com.intellij.modules.platform</depends>

    <resource-bundle>messages.MyBundle</resource-bundle>

    <extensions defaultExtensionNs="com.intellij">
        <toolWindow factoryClass="com.github.iptton.kbuilder.toolWindow.JewelToolWindowFactory" id="JewelToolWindow"/>
        <toolWindow factoryClass="com.github.iptton.kbuilder.toolWindow.MyToolWindowFactory" id="MyToolWindowFactory"/>
        <postStartupActivity implementation="com.github.iptton.kbuilder.startup.MyProjectActivity" />

        <!-- GitHub Copilot Services -->
        <applicationService serviceImplementation="com.github.iptton.kbuilder.auth.TokenStorage"/>
        <applicationService serviceImplementation="com.github.iptton.kbuilder.auth.GitHubDeviceFlowAuth"/>
        <applicationService serviceImplementation="com.github.iptton.kbuilder.auth.AutomaticTokenRefreshService"/>
        <applicationService serviceImplementation="com.github.iptton.kbuilder.copilot.CopilotTokenManager"/>
        <applicationService serviceImplementation="com.github.iptton.kbuilder.copilot.CopilotApiService"/>

        <!-- Startup Activities -->
        <postStartupActivity implementation="com.github.iptton.kbuilder.auth.TokenRefreshServiceInitializer"/>
    </extensions>
</idea-plugin>
