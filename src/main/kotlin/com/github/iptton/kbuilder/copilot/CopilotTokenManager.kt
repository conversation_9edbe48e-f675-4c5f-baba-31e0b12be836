package com.github.iptton.kbuilder.copilot

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.github.iptton.kbuilder.auth.TokenStorage
import com.github.iptton.kbuilder.auth.CopilotTokenResponse
import com.github.iptton.kbuilder.auth.AuthResult
import com.google.gson.Gson
import okhttp3.*
import java.io.IOException
import java.time.Instant
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.*

/**
 * Manages GitHub Copilot internal tokens
 */
@Service
class CopilotTokenManager {

    private val tokenStorage = service<TokenStorage>()
    
    private val logger = thisLogger()
    private val gson = Gson()
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    companion object {
        private const val COPILOT_TOKEN_URL = "https://api.github.com/copilot_internal/v2/token"
        private const val USER_AGENT = "GitHub-Copilot-IntelliJ-Plugin/1.0"
        private const val TOKEN_REFRESH_BUFFER_SECONDS = 300 // Refresh 5 minutes before expiry
    }
    
    /**
     * Get a valid Copilot internal token, refreshing if necessary
     */
    suspend fun getValidCopilotToken(): AuthResult<String> = withContext(Dispatchers.IO) {
        try {
            // Check if we have a valid cached token
            val cachedToken = tokenStorage.getCopilotToken()
            if (cachedToken != null && !tokenStorage.isCopilotTokenExpired()) {
                logger.debug("Using cached Copilot token")
                return@withContext AuthResult.Success(cachedToken)
            }
            
            // Need to fetch a new token
            logger.info("Fetching new Copilot internal token")
            fetchCopilotToken()
        } catch (e: Exception) {
            logger.error("Error getting Copilot token", e)
            AuthResult.Error("Failed to get Copilot token: ${e.message}", e)
        }
    }
    
    /**
     * Fetch a new Copilot internal token from GitHub
     */
    private suspend fun fetchCopilotToken(): AuthResult<String> = withContext(Dispatchers.IO) {
        try {
            val githubToken = tokenStorage.getGitHubToken()
            if (githubToken == null) {
                return@withContext AuthResult.Error("No GitHub token available. Please authenticate first.")
            }
            
            if (tokenStorage.isGitHubTokenExpired()) {
                // Try to refresh GitHub token if refresh token is available
                val githubAuth = service<com.github.iptton.kbuilder.auth.GitHubDeviceFlowAuth>()
                if (githubAuth.hasRefreshToken()) {
                    logger.info("GitHub token expired, attempting to refresh...")
                    val refreshResult = githubAuth.refreshAccessToken()
                    when (refreshResult) {
                        is AuthResult.Success -> {
                            logger.info("GitHub token refreshed successfully, retrying Copilot token fetch")
                            // Continue with the original flow using the refreshed token
                        }
                        is AuthResult.Error -> {
                            return@withContext AuthResult.Error("GitHub token expired and refresh failed: ${refreshResult.message}")
                        }
                        else -> {
                            return@withContext AuthResult.Error("GitHub token expired and refresh returned unexpected result")
                        }
                    }
                } else {
                    return@withContext AuthResult.Error("GitHub token has expired and no refresh token available. Please re-authenticate.")
                }
            }

            // Get the GitHub token again (it might have been refreshed)
            val currentGithubToken = tokenStorage.getGitHubToken()
            if (currentGithubToken == null) {
                return@withContext AuthResult.Error("No GitHub token available after refresh attempt.")
            }

            val request = Request.Builder()
                .url(COPILOT_TOKEN_URL)
                .get()
                .header("Authorization", "token $currentGithubToken")
                .header("Accept", "application/json")
                .header("User-Agent", USER_AGENT)
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val tokenResponse = gson.fromJson(responseBody, CopilotTokenResponse::class.java)
                    
                    // Calculate expiry time (tokens typically last 1 hour)
                    val expiresAt = tokenResponse.expires_at ?: (Instant.now().epochSecond + 3600)
                    
                    // Store the token
                    tokenStorage.storeCopilotToken(tokenResponse.token, expiresAt)
                    
                    logger.info("Copilot internal token fetched successfully")
                    AuthResult.Success(tokenResponse.token)
                } else {
                    AuthResult.Error("Empty response from Copilot token endpoint")
                }
            } else {
                val errorBody = response.body?.string()
                logger.error("Failed to fetch Copilot token: HTTP ${response.code} - $errorBody")
                
                when (response.code) {
                    401 -> AuthResult.Error("GitHub token is invalid or expired. Please re-authenticate.")
                    403 -> AuthResult.Error("Access denied. You may not have Copilot access or the token lacks required permissions.")
                    404 -> AuthResult.Error("Copilot token endpoint not found. This may indicate a service issue.")
                    429 -> AuthResult.Error("Rate limit exceeded. Please try again later.")
                    else -> AuthResult.Error("Failed to fetch Copilot token: HTTP ${response.code}")
                }
            }
        } catch (e: IOException) {
            logger.error("Network error fetching Copilot token", e)
            AuthResult.Error("Network error: ${e.message}", e)
        } catch (e: Exception) {
            logger.error("Unexpected error fetching Copilot token", e)
            AuthResult.Error("Unexpected error: ${e.message}", e)
        }
    }
    
    /**
     * Force refresh the Copilot token
     */
    suspend fun refreshCopilotToken(): AuthResult<String> = withContext(Dispatchers.IO) {
        logger.info("Force refreshing Copilot token")
        fetchCopilotToken()
    }
    
    /**
     * Check if the current Copilot token needs refresh
     */
    fun needsRefresh(): Boolean {
        val token = tokenStorage.getCopilotToken()
        if (token == null) return true
        
        // Check if token is expired or will expire soon
        return tokenStorage.isCopilotTokenExpired() || isTokenExpiringSoon()
    }
    
    /**
     * Check if token is expiring soon (within buffer time)
     */
    private fun isTokenExpiringSoon(): Boolean {
        val metadata = tokenStorage.getTokenMetadata()
        return metadata.copilotTokenExpiry?.let { expiry ->
            val now = Instant.now().epochSecond
            val timeUntilExpiry = expiry - now
            timeUntilExpiry <= TOKEN_REFRESH_BUFFER_SECONDS
        } ?: false
    }
    
    /**
     * Clear the cached Copilot token
     */
    fun clearCopilotToken() {
        try {
            // We need to clear only the Copilot token, not all tokens
            // This is a simplified approach - in a real implementation you might want
            // more granular control over individual token clearing
            val githubToken = tokenStorage.getGitHubToken()
            tokenStorage.clearAllTokens()
            if (githubToken != null) {
                tokenStorage.storeGitHubToken(githubToken)
            }
            logger.info("Copilot token cleared")
        } catch (e: Exception) {
            logger.error("Error clearing Copilot token", e)
        }
    }
    
    /**
     * Get token status information
     */
    fun getTokenStatus(): TokenStatus {
        val copilotToken = tokenStorage.getCopilotToken()
        val githubToken = tokenStorage.getGitHubToken()
        
        return TokenStatus(
            hasCopilotToken = copilotToken != null,
            hasGitHubToken = githubToken != null,
            isCopilotTokenExpired = tokenStorage.isCopilotTokenExpired(),
            isGitHubTokenExpired = tokenStorage.isGitHubTokenExpired(),
            needsRefresh = needsRefresh()
        )
    }
    
    /**
     * Token status information
     */
    data class TokenStatus(
        val hasCopilotToken: Boolean,
        val hasGitHubToken: Boolean,
        val isCopilotTokenExpired: Boolean,
        val isGitHubTokenExpired: Boolean,
        val needsRefresh: Boolean
    ) {
        val isReady: Boolean
            get() = hasCopilotToken && hasGitHubToken && !isCopilotTokenExpired && !isGitHubTokenExpired
    }
}
