package com.github.iptton.kbuilder.ui

import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPanel
import com.intellij.util.ui.JBUI
import com.github.iptton.kbuilder.auth.*
import kotlinx.coroutines.*
import java.awt.BorderLayout
import java.awt.FlowLayout
import java.awt.Color
import javax.swing.*

/**
 * Panel that displays token status and provides refresh controls
 */
class TokenStatusPanel : JBPanel<TokenStatusPanel>(BorderLayout()), TokenRefreshListener {
    
    private val tokenStorage = service<TokenStorage>()
    private val refreshService = service<AutomaticTokenRefreshService>()
    
    private val logger = thisLogger()
    private val panelScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // UI Components
    private val githubTokenStatus = JBLabel()
    private val copilotTokenStatus = JBLabel()
    private val refreshServiceStatus = JBLabel()
    private val refreshButton = JButton("Refresh Tokens")
    private val statusPanel = JBPanel<JBPanel<*>>(FlowLayout(FlowLayout.LEFT))
    
    init {
        setupUI()
        setupEventHandlers()
        refreshService.addTokenRefreshListener(this)
        updateTokenStatus()
    }
    
    private fun setupUI() {
        border = JBUI.Borders.empty(10)
        
        // Status labels
        githubTokenStatus.text = "GitHub Token: Checking..."
        copilotTokenStatus.text = "Copilot Token: Checking..."
        refreshServiceStatus.text = "Auto Refresh: Checking..."
        
        // Status panel
        statusPanel.add(githubTokenStatus)
        statusPanel.add(Box.createHorizontalStrut(20))
        statusPanel.add(copilotTokenStatus)
        statusPanel.add(Box.createHorizontalStrut(20))
        statusPanel.add(refreshServiceStatus)
        
        // Button panel
        val buttonPanel = JBPanel<JBPanel<*>>(FlowLayout(FlowLayout.RIGHT))
        buttonPanel.add(refreshButton)
        
        add(statusPanel, BorderLayout.CENTER)
        add(buttonPanel, BorderLayout.EAST)
    }
    
    private fun setupEventHandlers() {
        refreshButton.addActionListener {
            refreshButton.isEnabled = false
            refreshButton.text = "Refreshing..."
            
            panelScope.launch {
                try {
                    val result = refreshService.forceRefreshAllTokens()
                    
                    SwingUtilities.invokeLater {
                        if (result.allSuccessful) {
                            JOptionPane.showMessageDialog(
                                this@TokenStatusPanel,
                                "All tokens refreshed successfully!",
                                "Refresh Complete",
                                JOptionPane.INFORMATION_MESSAGE
                            )
                        } else {
                            val errorMessage = buildString {
                                appendLine("Token refresh completed with errors:")
                                result.errors.forEach { (tokenType, error) ->
                                    appendLine("- $tokenType: $error")
                                }
                            }
                            JOptionPane.showMessageDialog(
                                this@TokenStatusPanel,
                                errorMessage,
                                "Refresh Errors",
                                JOptionPane.WARNING_MESSAGE
                            )
                        }
                        updateTokenStatus()
                    }
                } catch (e: Exception) {
                    logger.error("Error during manual token refresh", e)
                    SwingUtilities.invokeLater {
                        JOptionPane.showMessageDialog(
                            this@TokenStatusPanel,
                            "Failed to refresh tokens: ${e.message}",
                            "Refresh Error",
                            JOptionPane.ERROR_MESSAGE
                        )
                    }
                } finally {
                    SwingUtilities.invokeLater {
                        refreshButton.isEnabled = true
                        refreshButton.text = "Refresh Tokens"
                    }
                }
            }
        }
    }
    
    private fun updateTokenStatus() {
        panelScope.launch {
            try {
                val githubToken = tokenStorage.getGitHubToken()
                val copilotToken = tokenStorage.getCopilotToken()
                val githubExpired = tokenStorage.isGitHubTokenExpired()
                val copilotExpired = tokenStorage.isCopilotTokenExpired()
                val refreshServiceStatus = refreshService.getRefreshServiceStatus()
                
                SwingUtilities.invokeLater {
                    // GitHub token status
                    when {
                        githubToken == null -> {
                            githubTokenStatus.text = "GitHub Token: Not Available"
                            githubTokenStatus.foreground = Color.RED
                        }
                        githubExpired -> {
                            githubTokenStatus.text = "GitHub Token: Expired"
                            githubTokenStatus.foreground = Color.ORANGE
                        }
                        else -> {
                            githubTokenStatus.text = "GitHub Token: Valid"
                            githubTokenStatus.foreground = Color.GREEN
                        }
                    }
                    
                    // Copilot token status
                    when {
                        copilotToken == null -> {
                            <EMAIL> = "Copilot Token: Not Available"
                            <EMAIL> = Color.RED
                        }
                        copilotExpired -> {
                            <EMAIL> = "Copilot Token: Expired"
                            <EMAIL> = Color.ORANGE
                        }
                        else -> {
                            <EMAIL> = "Copilot Token: Valid"
                            <EMAIL> = Color.GREEN
                        }
                    }
                    
                    // Refresh service status
                    if (refreshServiceStatus.isRunning) {
                        <EMAIL> = "Auto Refresh: Running"
                        <EMAIL> = Color.GREEN
                    } else {
                        <EMAIL> = "Auto Refresh: Stopped"
                        <EMAIL> = Color.RED
                    }
                }
            } catch (e: Exception) {
                logger.error("Error updating token status", e)
            }
        }
    }
    
    override fun onTokenRefreshed(tokenType: TokenType, success: Boolean, error: String?) {
        SwingUtilities.invokeLater {
            updateTokenStatus()
            
            if (!success && error != null) {
                // Show a subtle notification for automatic refresh failures
                logger.warn("Automatic token refresh failed for $tokenType: $error")
            }
        }
    }
    
    override fun onReAuthenticationRequired(reason: String) {
        SwingUtilities.invokeLater {
            val result = JOptionPane.showConfirmDialog(
                this,
                "Re-authentication is required: $reason\n\nWould you like to authenticate now?",
                "Re-authentication Required",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE
            )
            
            if (result == JOptionPane.YES_OPTION) {
                // Trigger re-authentication
                // This would typically open the authentication dialog
                logger.info("User chose to re-authenticate")
                // TODO: Implement re-authentication trigger
            }
        }
    }
    
    /**
     * Clean up resources when the panel is disposed
     */
    fun dispose() {
        refreshService.removeTokenRefreshListener(this)
        panelScope.cancel()
    }
}
