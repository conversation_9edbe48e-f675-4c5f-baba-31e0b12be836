package com.github.iptton.kbuilder.ui

import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPanel
import com.intellij.util.ui.JBUI
import com.github.iptton.kbuilder.auth.*
import com.github.iptton.kbuilder.copilot.CopilotApiService
import kotlinx.coroutines.*
import java.awt.BorderLayout
import java.awt.GridBagConstraints
import java.awt.GridBagLayout
import javax.swing.*

/**
 * Settings panel for GitHub Copilot configuration
 */
class CopilotSettingsPanel : JBPanel<CopilotSettingsPanel>(BorderLayout()) {
    
    private val githubAuth = service<GitHubDeviceFlowAuth>()
    private val copilotApiService = service<CopilotApiService>()
    private val refreshService = service<AutomaticTokenRefreshService>()
    
    private val logger = thisLogger()
    private val panelScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // UI Components
    private val tokenStatusPanel = TokenStatusPanel()
    private val authButton = JButton("Authenticate with GitHub")
    private val signOutButton = JButton("Sign Out")
    private val testConnectionButton = JButton("Test Connection")
    private val statusLabel = JBLabel("Ready")
    
    init {
        setupUI()
        setupEventHandlers()
        updateAuthState()
    }
    
    private fun setupUI() {
        border = JBUI.Borders.empty(10)
        
        // Main content panel
        val contentPanel = JBPanel<JBPanel<*>>(GridBagLayout())
        val gbc = GridBagConstraints()
        
        // Title
        gbc.gridx = 0
        gbc.gridy = 0
        gbc.gridwidth = 2
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.insets = JBUI.insets(0, 0, 20, 0)
        contentPanel.add(JBLabel("<html><h2>GitHub Copilot Settings</h2></html>"), gbc)
        
        // Token status panel
        gbc.gridy = 1
        gbc.insets = JBUI.insets(0, 0, 20, 0)
        contentPanel.add(tokenStatusPanel, gbc)
        
        // Authentication section
        gbc.gridy = 2
        gbc.gridwidth = 1
        gbc.fill = GridBagConstraints.NONE
        gbc.anchor = GridBagConstraints.WEST
        gbc.insets = JBUI.insets(0, 0, 10, 10)
        contentPanel.add(authButton, gbc)
        
        gbc.gridx = 1
        gbc.insets = JBUI.insets(0, 0, 10, 0)
        contentPanel.add(signOutButton, gbc)
        
        // Test connection
        gbc.gridx = 0
        gbc.gridy = 3
        gbc.gridwidth = 2
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.insets = JBUI.insets(10, 0, 10, 0)
        contentPanel.add(testConnectionButton, gbc)
        
        // Status
        gbc.gridy = 4
        gbc.insets = JBUI.insets(10, 0, 0, 0)
        contentPanel.add(statusLabel, gbc)
        
        add(contentPanel, BorderLayout.NORTH)
    }
    
    private fun setupEventHandlers() {
        authButton.addActionListener {
            authenticateWithGitHub()
        }
        
        signOutButton.addActionListener {
            signOut()
        }
        
        testConnectionButton.addActionListener {
            testConnection()
        }
    }
    
    private fun authenticateWithGitHub() {
        authButton.isEnabled = false
        authButton.text = "Authenticating..."
        statusLabel.text = "Starting GitHub authentication..."
        
        panelScope.launch {
            try {
                val deviceFlowResult = githubAuth.startDeviceFlow()
                when (deviceFlowResult) {
                    is AuthResult.Success -> {
                        val deviceFlow = deviceFlowResult.data
                        SwingUtilities.invokeLater {
                            statusLabel.text = "Please visit: ${deviceFlow.verification_uri} and enter code: ${deviceFlow.user_code}"
                            
                            // Show dialog with instructions
                            val message = """
                                Please complete authentication in your browser:
                                
                                1. Visit: ${deviceFlow.verification_uri}
                                2. Enter code: ${deviceFlow.user_code}
                                3. Click OK after completing authentication
                            """.trimIndent()
                            
                            JOptionPane.showMessageDialog(
                                this@CopilotSettingsPanel,
                                message,
                                "GitHub Authentication",
                                JOptionPane.INFORMATION_MESSAGE
                            )
                        }
                        
                        // Start polling for token
                        githubAuth.startPolling(
                            deviceFlow,
                            onSuccess = { tokenResponse ->
                                SwingUtilities.invokeLater {
                                    statusLabel.text = "Authentication successful!"
                                    updateAuthState()
                                    JOptionPane.showMessageDialog(
                                        this@CopilotSettingsPanel,
                                        "Successfully authenticated with GitHub!",
                                        "Authentication Complete",
                                        JOptionPane.INFORMATION_MESSAGE
                                    )
                                }
                            },
                            onError = { error ->
                                SwingUtilities.invokeLater {
                                    statusLabel.text = "Authentication failed: $error"
                                    updateAuthState()
                                    JOptionPane.showMessageDialog(
                                        this@CopilotSettingsPanel,
                                        "Authentication failed: $error",
                                        "Authentication Error",
                                        JOptionPane.ERROR_MESSAGE
                                    )
                                }
                            },
                            onPending = { message ->
                                SwingUtilities.invokeLater {
                                    statusLabel.text = message
                                }
                            }
                        )
                    }
                    is AuthResult.Error -> {
                        SwingUtilities.invokeLater {
                            statusLabel.text = "Failed to start authentication: ${deviceFlowResult.message}"
                            updateAuthState()
                            JOptionPane.showMessageDialog(
                                this@CopilotSettingsPanel,
                                "Failed to start authentication: ${deviceFlowResult.message}",
                                "Authentication Error",
                                JOptionPane.ERROR_MESSAGE
                            )
                        }
                    }
                    else -> {
                        SwingUtilities.invokeLater {
                            statusLabel.text = "Unexpected authentication result"
                            updateAuthState()
                        }
                    }
                }
            } catch (e: Exception) {
                logger.error("Error during authentication", e)
                SwingUtilities.invokeLater {
                    statusLabel.text = "Authentication error: ${e.message}"
                    updateAuthState()
                }
            }
        }
    }
    
    private fun signOut() {
        githubAuth.signOut()
        refreshService.stopAutomaticRefresh()
        statusLabel.text = "Signed out successfully"
        updateAuthState()
        
        JOptionPane.showMessageDialog(
            this,
            "You have been signed out of GitHub Copilot.",
            "Sign Out Complete",
            JOptionPane.INFORMATION_MESSAGE
        )
    }
    
    private fun testConnection() {
        testConnectionButton.isEnabled = false
        testConnectionButton.text = "Testing..."
        statusLabel.text = "Testing connection to Copilot API..."
        
        panelScope.launch {
            try {
                val result = copilotApiService.getCachedModels()
                SwingUtilities.invokeLater {
                    when (result) {
                        is com.github.iptton.kbuilder.copilot.CopilotApiResult.Success -> {
                            statusLabel.text = "Connection test successful! Found ${result.data.size} models."
                            JOptionPane.showMessageDialog(
                                this@CopilotSettingsPanel,
                                "Connection test successful!\nFound ${result.data.size} available models.",
                                "Connection Test",
                                JOptionPane.INFORMATION_MESSAGE
                            )
                        }
                        is com.github.iptton.kbuilder.copilot.CopilotApiResult.Error -> {
                            statusLabel.text = "Connection test failed: ${result.message}"
                            JOptionPane.showMessageDialog(
                                this@CopilotSettingsPanel,
                                "Connection test failed: ${result.message}",
                                "Connection Test Failed",
                                JOptionPane.ERROR_MESSAGE
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                logger.error("Error during connection test", e)
                SwingUtilities.invokeLater {
                    statusLabel.text = "Connection test error: ${e.message}"
                }
            } finally {
                SwingUtilities.invokeLater {
                    testConnectionButton.isEnabled = true
                    testConnectionButton.text = "Test Connection"
                }
            }
        }
    }
    
    private fun updateAuthState() {
        val authState = githubAuth.getAuthState()
        
        when (authState) {
            AuthState.AUTHENTICATED -> {
                authButton.isEnabled = false
                authButton.text = "Authenticated"
                signOutButton.isEnabled = true
                testConnectionButton.isEnabled = true
                if (statusLabel.text == "Ready") {
                    statusLabel.text = "Authenticated and ready"
                }
            }
            AuthState.NOT_AUTHENTICATED -> {
                authButton.isEnabled = true
                authButton.text = "Authenticate with GitHub"
                signOutButton.isEnabled = false
                testConnectionButton.isEnabled = false
                if (statusLabel.text == "Ready") {
                    statusLabel.text = "Not authenticated"
                }
            }
            AuthState.TOKEN_EXPIRED -> {
                authButton.isEnabled = true
                authButton.text = "Re-authenticate"
                signOutButton.isEnabled = true
                testConnectionButton.isEnabled = false
                if (statusLabel.text == "Ready") {
                    statusLabel.text = "Token expired - please re-authenticate"
                }
            }
            AuthState.DEVICE_FLOW_PENDING -> {
                authButton.isEnabled = false
                authButton.text = "Authentication in progress..."
                signOutButton.isEnabled = false
                testConnectionButton.isEnabled = false
            }
            AuthState.ERROR -> {
                authButton.isEnabled = true
                authButton.text = "Retry Authentication"
                signOutButton.isEnabled = false
                testConnectionButton.isEnabled = false
                if (statusLabel.text == "Ready") {
                    statusLabel.text = "Authentication error"
                }
            }
        }
    }
    
    /**
     * Clean up resources when the panel is disposed
     */
    fun dispose() {
        tokenStatusPanel.dispose()
        panelScope.cancel()
    }
}
