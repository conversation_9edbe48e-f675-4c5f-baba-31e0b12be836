package com.github.iptton.kbuilder.auth

import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity

/**
 * Initializes the automatic token refresh service when a project is opened
 */
class TokenRefreshServiceInitializer : ProjectActivity {
    
    override suspend fun execute(project: Project) {
        // Start the automatic token refresh service
        val refreshService = service<AutomaticTokenRefreshService>()
        refreshService.startAutomaticRefresh()
    }
}
