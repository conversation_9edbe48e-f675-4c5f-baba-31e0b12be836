package com.github.iptton.kbuilder.auth

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.application.ApplicationManager
import kotlinx.coroutines.*
import java.util.concurrent.TimeUnit

/**
 * Service that automatically refreshes GitHub and Copilot tokens before they expire
 */
@Service
class AutomaticTokenRefreshService {
    
    private val tokenStorage = service<TokenStorage>()
    private val githubAuth = service<GitHubDeviceFlowAuth>()
    
    private val logger = thisLogger()
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Refresh check interval (every 5 minutes)
    private val refreshCheckInterval = TimeUnit.MINUTES.toMillis(5)
    
    // Token refresh listeners
    private val refreshListeners = mutableListOf<TokenRefreshListener>()
    
    private var refreshJob: Job? = null
    private var isRunning = false
    
    companion object {
        private const val REFRESH_BUFFER_SECONDS = 300 // 5 minutes before expiry
    }
    
    /**
     * Start the automatic token refresh service
     */
    fun startAutomaticRefresh() {
        if (isRunning) {
            logger.debug("Automatic token refresh service is already running")
            return
        }
        
        logger.info("Starting automatic token refresh service")
        isRunning = true
        
        refreshJob = serviceScope.launch {
            while (isActive && isRunning) {
                try {
                    checkAndRefreshTokens()
                } catch (e: Exception) {
                    logger.error("Error during automatic token refresh check", e)
                }
                
                delay(refreshCheckInterval)
            }
        }
    }
    
    /**
     * Stop the automatic token refresh service
     */
    fun stopAutomaticRefresh() {
        logger.info("Stopping automatic token refresh service")
        isRunning = false
        refreshJob?.cancel()
        refreshJob = null
    }
    
    /**
     * Check if tokens need refresh and refresh them if necessary
     */
    private suspend fun checkAndRefreshTokens() {
        logger.debug("Checking tokens for refresh...")
        
        // Check GitHub token
        if (shouldRefreshGitHubToken()) {
            logger.info("GitHub token needs refresh")
            refreshGitHubToken()
        }
        
        // Check Copilot token
        if (shouldRefreshCopilotToken()) {
            logger.info("Copilot token needs refresh")
            refreshCopilotToken()
        }
    }
    
    /**
     * Check if GitHub token should be refreshed
     */
    private fun shouldRefreshGitHubToken(): Boolean {
        val hasToken = tokenStorage.getGitHubToken() != null
        val hasRefreshToken = tokenStorage.getGitHubRefreshToken() != null
        val isNearExpiry = tokenStorage.isGitHubTokenNearExpiry()
        val isExpired = tokenStorage.isGitHubTokenExpired()
        
        return hasToken && hasRefreshToken && (isNearExpiry || isExpired)
    }
    
    /**
     * Check if Copilot token should be refreshed
     */
    private fun shouldRefreshCopilotToken(): Boolean {
        val hasToken = tokenStorage.getCopilotToken() != null
        val hasGitHubToken = tokenStorage.getGitHubToken() != null
        val isNearExpiry = tokenStorage.isCopilotTokenNearExpiry()
        val isExpired = tokenStorage.isCopilotTokenExpired()
        val isGitHubTokenValid = !tokenStorage.isGitHubTokenExpired()
        
        return hasToken && hasGitHubToken && isGitHubTokenValid && (isNearExpiry || isExpired)
    }
    
    /**
     * Refresh GitHub token using refresh token
     */
    private suspend fun refreshGitHubToken() {
        try {
            logger.info("Attempting to refresh GitHub token")
            
            val result = githubAuth.refreshAccessToken()
            when (result) {
                is AuthResult.Success -> {
                    logger.info("GitHub token refreshed successfully")
                    notifyTokenRefreshed(TokenType.GITHUB, true, null)
                }
                is AuthResult.Error -> {
                    logger.error("Failed to refresh GitHub token: ${result.message}")
                    notifyTokenRefreshed(TokenType.GITHUB, false, result.message)

                    // If refresh fails, user needs to re-authenticate
                    notifyReAuthenticationRequired("GitHub token refresh failed: ${result.message}")
                }
                is AuthResult.Pending -> {
                    logger.warn("Unexpected pending result from GitHub token refresh: $result")
                    notifyTokenRefreshed(TokenType.GITHUB, false, "Unexpected pending state")
                }
            }
        } catch (e: Exception) {
            logger.error("Exception during GitHub token refresh", e)
            notifyTokenRefreshed(TokenType.GITHUB, false, e.message)
        }
    }
    
    /**
     * Refresh Copilot token
     */
    private suspend fun refreshCopilotToken() {
        try {
            logger.info("Attempting to refresh Copilot token")
            
            // First check if GitHub token is still valid
            if (tokenStorage.isGitHubTokenExpired()) {
                logger.warn("Cannot refresh Copilot token: GitHub token is expired")
                notifyTokenRefreshed(TokenType.COPILOT, false, "GitHub token is expired")
                return
            }
            
            val copilotTokenManager = service<com.github.iptton.kbuilder.copilot.CopilotTokenManager>()
            val result = copilotTokenManager.refreshCopilotToken()
            
            when (result) {
                is AuthResult.Success -> {
                    logger.info("Copilot token refreshed successfully")
                    notifyTokenRefreshed(TokenType.COPILOT, true, null)
                }
                is AuthResult.Error -> {
                    logger.error("Failed to refresh Copilot token: ${result.message}")
                    notifyTokenRefreshed(TokenType.COPILOT, false, result.message)
                }
                is AuthResult.Pending -> {
                    logger.warn("Unexpected pending result from Copilot token refresh: $result")
                    notifyTokenRefreshed(TokenType.COPILOT, false, "Unexpected pending state")
                }
            }
        } catch (e: Exception) {
            logger.error("Exception during Copilot token refresh", e)
            notifyTokenRefreshed(TokenType.COPILOT, false, e.message)
        }
    }
    
    /**
     * Add a token refresh listener
     */
    fun addTokenRefreshListener(listener: TokenRefreshListener) {
        refreshListeners.add(listener)
    }
    
    /**
     * Remove a token refresh listener
     */
    fun removeTokenRefreshListener(listener: TokenRefreshListener) {
        refreshListeners.remove(listener)
    }
    
    /**
     * Notify listeners of token refresh result
     */
    private fun notifyTokenRefreshed(tokenType: TokenType, success: Boolean, error: String?) {
        ApplicationManager.getApplication().invokeLater {
            refreshListeners.forEach { listener ->
                try {
                    listener.onTokenRefreshed(tokenType, success, error)
                } catch (e: Exception) {
                    logger.error("Error notifying token refresh listener", e)
                }
            }
        }
    }
    
    /**
     * Notify listeners that re-authentication is required
     */
    private fun notifyReAuthenticationRequired(reason: String) {
        ApplicationManager.getApplication().invokeLater {
            refreshListeners.forEach { listener ->
                try {
                    listener.onReAuthenticationRequired(reason)
                } catch (e: Exception) {
                    logger.error("Error notifying re-authentication listener", e)
                }
            }
        }
    }
    
    /**
     * Force refresh all tokens
     */
    suspend fun forceRefreshAllTokens(): RefreshResult {
        logger.info("Force refreshing all tokens")
        
        val results = mutableMapOf<TokenType, Boolean>()
        val errors = mutableMapOf<TokenType, String>()
        
        // Refresh GitHub token if possible
        if (tokenStorage.getGitHubRefreshToken() != null) {
            try {
                val result = githubAuth.refreshAccessToken()
                when (result) {
                    is AuthResult.Success -> {
                        results[TokenType.GITHUB] = true
                        logger.info("GitHub token force refresh successful")
                    }
                    is AuthResult.Error -> {
                        results[TokenType.GITHUB] = false
                        errors[TokenType.GITHUB] = result.message
                        logger.error("GitHub token force refresh failed: ${result.message}")
                    }
                    is AuthResult.Pending -> {
                        results[TokenType.GITHUB] = false
                        errors[TokenType.GITHUB] = "Unexpected pending state"
                        logger.error("GitHub token force refresh returned pending state")
                    }
                }
            } catch (e: Exception) {
                results[TokenType.GITHUB] = false
                errors[TokenType.GITHUB] = e.message ?: "Unknown error"
                logger.error("Exception during GitHub token force refresh", e)
            }
        }
        
        // Refresh Copilot token if GitHub token is valid
        if (!tokenStorage.isGitHubTokenExpired()) {
            try {
                val copilotTokenManager = service<com.github.iptton.kbuilder.copilot.CopilotTokenManager>()
                val result = copilotTokenManager.refreshCopilotToken()
                when (result) {
                    is AuthResult.Success -> {
                        results[TokenType.COPILOT] = true
                        logger.info("Copilot token force refresh successful")
                    }
                    is AuthResult.Error -> {
                        results[TokenType.COPILOT] = false
                        errors[TokenType.COPILOT] = result.message
                        logger.error("Copilot token force refresh failed: ${result.message}")
                    }
                    is AuthResult.Pending -> {
                        results[TokenType.COPILOT] = false
                        errors[TokenType.COPILOT] = "Unexpected pending state"
                        logger.error("Copilot token force refresh returned pending state")
                    }
                }
            } catch (e: Exception) {
                results[TokenType.COPILOT] = false
                errors[TokenType.COPILOT] = e.message ?: "Unknown error"
                logger.error("Exception during Copilot token force refresh", e)
            }
        } else {
            results[TokenType.COPILOT] = false
            errors[TokenType.COPILOT] = "GitHub token is expired"
        }
        
        return RefreshResult(results, errors)
    }
    
    /**
     * Get the current status of the refresh service
     */
    fun getRefreshServiceStatus(): RefreshServiceStatus {
        return RefreshServiceStatus(
            isRunning = isRunning,
            nextCheckIn = if (isRunning) refreshCheckInterval else null,
            githubTokenNeedsRefresh = shouldRefreshGitHubToken(),
            copilotTokenNeedsRefresh = shouldRefreshCopilotToken()
        )
    }
}

/**
 * Token types for refresh notifications
 */
enum class TokenType {
    GITHUB,
    COPILOT
}

/**
 * Interface for token refresh event listeners
 */
interface TokenRefreshListener {
    fun onTokenRefreshed(tokenType: TokenType, success: Boolean, error: String?)
    fun onReAuthenticationRequired(reason: String)
}

/**
 * Result of force refresh operation
 */
data class RefreshResult(
    val results: Map<TokenType, Boolean>,
    val errors: Map<TokenType, String>
) {
    val allSuccessful: Boolean
        get() = results.values.all { it }
    
    val hasErrors: Boolean
        get() = errors.isNotEmpty()
}

/**
 * Status of the refresh service
 */
data class RefreshServiceStatus(
    val isRunning: Boolean,
    val nextCheckIn: Long?,
    val githubTokenNeedsRefresh: Boolean,
    val copilotTokenNeedsRefresh: Boolean
)
