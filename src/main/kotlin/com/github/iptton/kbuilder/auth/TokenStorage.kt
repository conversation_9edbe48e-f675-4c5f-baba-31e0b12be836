package com.github.iptton.kbuilder.auth

import com.intellij.credentialStore.CredentialAttributes
import com.intellij.credentialStore.Credentials
import com.intellij.credentialStore.generateServiceName
import com.intellij.ide.passwordSafe.PasswordSafe
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import com.google.gson.Gson
import java.time.Instant

/**
 * Secure storage for GitHub and Copilot tokens using IntelliJ's credential store
 */
@Service
class TokenStorage {
    
    private val logger = thisLogger()
    private val gson = Gson()
    
    companion object {
        private const val SERVICE_NAME = "GitHub Copilot Plugin"
        private const val GITHUB_TOKEN_KEY = "github_access_token"
        private const val GITHUB_REFRESH_TOKEN_KEY = "github_refresh_token"
        private const val COPILOT_TOKEN_KEY = "copilot_internal_token"
        private const val TOKEN_METADATA_KEY = "token_metadata"
    }
    
    /**
     * Token metadata for expiration tracking
     */
    data class TokenMetadata(
        val githubTokenExpiry: Long? = null,
        val copilotTokenExpiry: Long? = null,
        val lastRefresh: Long = Instant.now().epochSecond,
        val githubRefreshTokenExpiry: Long? = null,
        val lastGithubRefresh: Long? = null,
        val lastCopilotRefresh: Long? = null
    )
    
    /**
     * Store GitHub access token securely
     */
    fun storeGitHubToken(token: String, expiresIn: Int? = null, refreshToken: String? = null) {
        try {
            val credentialAttributes = CredentialAttributes(
                generateServiceName(SERVICE_NAME, GITHUB_TOKEN_KEY)
            )
            val credentials = Credentials(GITHUB_TOKEN_KEY, token)
            PasswordSafe.instance.set(credentialAttributes, credentials)

            // Store refresh token if provided
            refreshToken?.let { storeGitHubRefreshToken(it) }

            // Store metadata with expiration info
            val metadata = getTokenMetadata().copy(
                githubTokenExpiry = expiresIn?.let { Instant.now().epochSecond + it },
                lastRefresh = Instant.now().epochSecond,
                lastGithubRefresh = Instant.now().epochSecond
            )
            storeTokenMetadata(metadata)

            logger.info("GitHub token stored successfully")
        } catch (e: Exception) {
            logger.error("Failed to store GitHub token", e)
            throw e
        }
    }
    
    /**
     * Retrieve GitHub access token
     */
    fun getGitHubToken(): String? {
        return try {
            val credentialAttributes = CredentialAttributes(
                generateServiceName(SERVICE_NAME, GITHUB_TOKEN_KEY)
            )
            PasswordSafe.instance.getPassword(credentialAttributes)
        } catch (e: Exception) {
            logger.error("Failed to retrieve GitHub token", e)
            null
        }
    }
    
    /**
     * Store Copilot internal token securely
     */
    fun storeCopilotToken(token: String, expiresAt: Long? = null) {
        try {
            val credentialAttributes = CredentialAttributes(
                generateServiceName(SERVICE_NAME, COPILOT_TOKEN_KEY)
            )
            val credentials = Credentials(COPILOT_TOKEN_KEY, token)
            PasswordSafe.instance.set(credentialAttributes, credentials)
            
            // Store metadata with expiration info
            val metadata = getTokenMetadata().copy(
                copilotTokenExpiry = expiresAt,
                lastRefresh = Instant.now().epochSecond,
                lastCopilotRefresh = Instant.now().epochSecond
            )
            storeTokenMetadata(metadata)
            
            logger.info("Copilot token stored successfully")
        } catch (e: Exception) {
            logger.error("Failed to store Copilot token", e)
            throw e
        }
    }
    
    /**
     * Retrieve Copilot internal token
     */
    fun getCopilotToken(): String? {
        return try {
            val credentialAttributes = CredentialAttributes(
                generateServiceName(SERVICE_NAME, COPILOT_TOKEN_KEY)
            )
            PasswordSafe.instance.getPassword(credentialAttributes)
        } catch (e: Exception) {
            logger.error("Failed to retrieve Copilot token", e)
            null
        }
    }
    
    /**
     * Check if GitHub token is expired
     */
    fun isGitHubTokenExpired(): Boolean {
        val metadata = getTokenMetadata()
        return metadata.githubTokenExpiry?.let { expiry ->
            Instant.now().epochSecond >= expiry
        } ?: false
    }
    
    /**
     * Check if Copilot token is expired
     */
    fun isCopilotTokenExpired(): Boolean {
        val metadata = getTokenMetadata()
        return metadata.copilotTokenExpiry?.let { expiry ->
            Instant.now().epochSecond >= expiry
        } ?: false
    }
    
    /**
     * Store GitHub refresh token securely
     */
    private fun storeGitHubRefreshToken(refreshToken: String) {
        try {
            val credentialAttributes = CredentialAttributes(
                generateServiceName(SERVICE_NAME, GITHUB_REFRESH_TOKEN_KEY)
            )
            val credentials = Credentials(GITHUB_REFRESH_TOKEN_KEY, refreshToken)
            PasswordSafe.instance.set(credentialAttributes, credentials)
            logger.debug("GitHub refresh token stored successfully")
        } catch (e: Exception) {
            logger.error("Failed to store GitHub refresh token", e)
            throw e
        }
    }

    /**
     * Retrieve GitHub refresh token
     */
    fun getGitHubRefreshToken(): String? {
        return try {
            val credentialAttributes = CredentialAttributes(
                generateServiceName(SERVICE_NAME, GITHUB_REFRESH_TOKEN_KEY)
            )
            PasswordSafe.instance.getPassword(credentialAttributes)
        } catch (e: Exception) {
            logger.error("Failed to retrieve GitHub refresh token", e)
            null
        }
    }

    /**
     * Check if GitHub token needs refresh (expires within 5 minutes)
     */
    fun isGitHubTokenNearExpiry(): Boolean {
        val metadata = getTokenMetadata()
        return metadata.githubTokenExpiry?.let { expiry ->
            val now = Instant.now().epochSecond
            val timeUntilExpiry = expiry - now
            timeUntilExpiry <= 300 // 5 minutes buffer
        } ?: false
    }

    /**
     * Check if Copilot token needs refresh (expires within 5 minutes)
     */
    fun isCopilotTokenNearExpiry(): Boolean {
        val metadata = getTokenMetadata()
        return metadata.copilotTokenExpiry?.let { expiry ->
            val now = Instant.now().epochSecond
            val timeUntilExpiry = expiry - now
            timeUntilExpiry <= 300 // 5 minutes buffer
        } ?: false
    }

    /**
     * Clear all stored tokens
     */
    fun clearAllTokens() {
        try {
            val githubCredentials = CredentialAttributes(
                generateServiceName(SERVICE_NAME, GITHUB_TOKEN_KEY)
            )
            val githubRefreshCredentials = CredentialAttributes(
                generateServiceName(SERVICE_NAME, GITHUB_REFRESH_TOKEN_KEY)
            )
            val copilotCredentials = CredentialAttributes(
                generateServiceName(SERVICE_NAME, COPILOT_TOKEN_KEY)
            )
            val metadataCredentials = CredentialAttributes(
                generateServiceName(SERVICE_NAME, TOKEN_METADATA_KEY)
            )

            PasswordSafe.instance.set(githubCredentials, null)
            PasswordSafe.instance.set(githubRefreshCredentials, null)
            PasswordSafe.instance.set(copilotCredentials, null)
            PasswordSafe.instance.set(metadataCredentials, null)

            logger.info("All tokens cleared successfully")
        } catch (e: Exception) {
            logger.error("Failed to clear tokens", e)
            throw e
        }
    }
    
    private fun storeTokenMetadata(metadata: TokenMetadata) {
        try {
            val credentialAttributes = CredentialAttributes(
                generateServiceName(SERVICE_NAME, TOKEN_METADATA_KEY)
            )
            val metadataJson = gson.toJson(metadata)
            val credentials = Credentials(TOKEN_METADATA_KEY, metadataJson)
            PasswordSafe.instance.set(credentialAttributes, credentials)
        } catch (e: Exception) {
            logger.error("Failed to store token metadata", e)
        }
    }
    
    fun getTokenMetadata(): TokenMetadata {
        return try {
            val credentialAttributes = CredentialAttributes(
                generateServiceName(SERVICE_NAME, TOKEN_METADATA_KEY)
            )
            val metadataJson = PasswordSafe.instance.getPassword(credentialAttributes)
            if (metadataJson != null) {
                gson.fromJson(metadataJson, TokenMetadata::class.java)
            } else {
                TokenMetadata()
            }
        } catch (e: Exception) {
            logger.error("Failed to retrieve token metadata", e)
            TokenMetadata()
        }
    }
}
