# Automatic Token Refresh

This document describes the automatic token refresh functionality implemented in the GitHub Copilot plugin.

## Overview

The plugin implements automatic token refresh to ensure uninterrupted access to GitHub Copilot services. When tokens are about to expire, the system automatically attempts to refresh them in the background.

## Components

### 1. AutomaticTokenRefreshService

The main service responsible for automatic token refresh:

- **Background monitoring**: Checks token expiration every 5 minutes
- **Proactive refresh**: Refreshes tokens 5 minutes before they expire
- **Error handling**: Gracefully handles refresh failures and notifies listeners
- **Manual refresh**: Provides API for manual token refresh

### 2. Enhanced TokenStorage

Extended to support refresh tokens:

- **Refresh token storage**: Securely stores GitHub refresh tokens
- **Expiration checking**: Methods to check if tokens are near expiry
- **Metadata tracking**: Tracks last refresh times for both token types

### 3. Enhanced GitHubDeviceFlowAuth

Updated to handle refresh tokens:

- **Refresh token support**: Stores refresh tokens during initial authentication
- **Token refresh**: Implements GitHub OAuth refresh token flow
- **Fallback handling**: Handles cases where refresh tokens are invalid

### 4. Enhanced CopilotTokenManager

Improved to handle GitHub token expiration:

- **Automatic GitHub refresh**: Attempts to refresh expired GitHub tokens
- **Seamless operation**: Continues Copilot token fetching after GitHub token refresh
- **Error propagation**: Properly handles and reports refresh failures

## Token Refresh Flow

### GitHub Token Refresh

1. **Detection**: Service detects GitHub token is near expiry (< 5 minutes)
2. **Refresh attempt**: Uses stored refresh token to get new access token
3. **Success**: Stores new access token and refresh token
4. **Failure**: Notifies listeners that re-authentication is required

### Copilot Token Refresh

1. **Detection**: Service detects Copilot token is near expiry (< 5 minutes)
2. **GitHub token check**: Ensures GitHub token is valid (refreshes if needed)
3. **Copilot refresh**: Fetches new Copilot token using valid GitHub token
4. **Storage**: Stores new Copilot token with expiration metadata

## Configuration

### Service Startup

The `TokenRefreshServiceInitializer` automatically starts the refresh service when a project is opened:

```kotlin
class TokenRefreshServiceInitializer : ProjectActivity {
    override suspend fun execute(project: Project) {
        val refreshService = service<AutomaticTokenRefreshService>()
        refreshService.startAutomaticRefresh()
    }
}
```

### Refresh Intervals

- **Check interval**: 5 minutes
- **Refresh buffer**: 5 minutes before expiry
- **Retry logic**: Built into individual refresh operations

## UI Integration

### TokenStatusPanel

Provides visual feedback on token status:

- **Real-time status**: Shows current token validity
- **Manual refresh**: Button to force token refresh
- **Error notifications**: Displays refresh failures

### Event Listeners

Components can implement `TokenRefreshListener` to receive notifications:

```kotlin
interface TokenRefreshListener {
    fun onTokenRefreshed(tokenType: TokenType, success: Boolean, error: String?)
    fun onReAuthenticationRequired(reason: String)
}
```

## Error Handling

### Refresh Failures

When automatic refresh fails:

1. **Logging**: Error is logged with details
2. **Notification**: Listeners are notified of the failure
3. **User prompt**: UI may prompt user for re-authentication
4. **Graceful degradation**: Service continues monitoring other tokens

### Network Issues

- **Retry logic**: Built into HTTP operations
- **Timeout handling**: Reasonable timeouts prevent hanging
- **Fallback**: Manual refresh options remain available

### Invalid Refresh Tokens

When refresh tokens become invalid:

1. **Detection**: Refresh attempt returns invalid_grant error
2. **Cleanup**: Invalid tokens are cleared from storage
3. **Notification**: User is prompted to re-authenticate
4. **Recovery**: Full device flow authentication can be restarted

## Security Considerations

### Token Storage

- **Secure storage**: Uses IntelliJ's PasswordSafe for token storage
- **Encryption**: Tokens are encrypted at rest
- **Access control**: Only the plugin can access stored tokens

### Refresh Token Handling

- **Limited scope**: Refresh tokens only have copilot scope
- **Secure transmission**: HTTPS for all token operations
- **Automatic cleanup**: Invalid tokens are automatically removed

## Testing

### Unit Tests

The `AutomaticTokenRefreshServiceTest` covers:

- **Refresh triggers**: When tokens should be refreshed
- **Success scenarios**: Successful refresh operations
- **Failure handling**: Graceful handling of refresh failures
- **Edge cases**: Missing tokens, network errors, etc.

### Manual Testing

1. **Authenticate**: Complete GitHub device flow authentication
2. **Monitor**: Observe automatic refresh in logs
3. **Force expiry**: Manually expire tokens to test refresh
4. **Network issues**: Test with network disconnected
5. **Invalid tokens**: Test with corrupted refresh tokens

## Troubleshooting

### Common Issues

1. **No refresh token**: User needs to re-authenticate
2. **Network errors**: Check internet connection
3. **Invalid tokens**: Clear tokens and re-authenticate
4. **Service not running**: Check if AutomaticTokenRefreshService started

### Debugging

Enable debug logging to see refresh operations:

```
2024-01-01 12:00:00 DEBUG AutomaticTokenRefreshService - Checking tokens for refresh...
2024-01-01 12:00:00 INFO  AutomaticTokenRefreshService - GitHub token needs refresh
2024-01-01 12:00:01 INFO  GitHubDeviceFlowAuth - Refreshing GitHub access token
2024-01-01 12:00:02 INFO  GitHubDeviceFlowAuth - GitHub access token refreshed successfully
```

## Future Enhancements

### Planned Improvements

1. **Smart refresh timing**: Adjust refresh timing based on usage patterns
2. **Batch operations**: Refresh multiple tokens in single operation
3. **Offline support**: Cache tokens for offline operation
4. **Health monitoring**: Monitor refresh success rates
5. **User preferences**: Allow users to configure refresh behavior

### API Extensions

1. **Refresh callbacks**: More granular refresh event callbacks
2. **Token validation**: API to validate token without using it
3. **Refresh scheduling**: Custom refresh schedules per token type
4. **Metrics collection**: Collect refresh performance metrics
